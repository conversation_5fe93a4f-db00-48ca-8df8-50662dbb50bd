﻿  SnPhyFluid.cpp
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(49,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/SnPhyFluid.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\MMDPhysics.h(335,63): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/SnPhyFluid.cpp')
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(11,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/SnPhyFluid.cpp')
      D:\AProj\UaIrrlicht\include\ISceneNode.h(40,7):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8): warning C4099: 'EQVisual::SvgManager': type name first seen using 'struct' now seen using 'class'
  (compiling source file '/src/irrmmd/SnPhyFluid.cpp')
      D:\AProj\AppMainLib\src\IrrFw\SvgMan.h(161,8):
      see declaration of 'EQVisual::SvgManager'
  
D:\AProj\CommonStaticLib\saba\src\Saba\Model\MMD\VMDAnimation.h(305,13): warning C4018: '>': signed/unsigned mismatch
  (compiling source file '/src/irrmmd/SnPhyFluid.cpp')
  
D:\AProj\UaIrrlicht\source\Irrlicht\VulkanRenderer\VkFixedFunctionMaterialRenderer.h(218,40): warning C4267: 'return': conversion from 'size_t' to 'uint32_t', possible loss of data
  (compiling source file '/src/irrmmd/SnPhyFluid.cpp')
  
D:\AProj\CommonStaticLib\mmdFormats\EncodingHelper.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/SnPhyFluid.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\VmdEventExt.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file '/src/irrmmd/SnPhyFluid.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9): warning C4099: 'irr::scene::PhyObj': type name first seen using 'class' now seen using 'struct'
  (compiling source file '/src/irrmmd/SnPhyFluid.cpp')
      D:\AProj\AppMainLib\src\irrmmd\PhyObjMan.h(35,9):
      see declaration of 'irr::scene::PhyObj'
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(354,41): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/SnPhyFluid.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(355,47): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/SnPhyFluid.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\MmdPhyAnimator.h(357,33): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file '/src/irrmmd/SnPhyFluid.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyFluid.cpp(85,55): warning C4311: 'type cast': pointer truncation from 'irr::scene::SnPhyFluid *' to 'physx::PxU32'
  (compiling source file '/src/irrmmd/SnPhyFluid.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyFluid.cpp(85,55): warning C4302: 'type cast': truncation from 'irr::scene::SnPhyFluid *' to 'physx::PxU32'
  (compiling source file '/src/irrmmd/SnPhyFluid.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyFluid.cpp(427,17): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/SnPhyFluid.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyFluid.cpp(432,29): warning C4267: '=': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/SnPhyFluid.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyFluid.cpp(489,17): warning C4267: 'argument': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/SnPhyFluid.cpp')
  
D:\AProj\AppMainLib\src\irrmmd\SnPhyFluid.cpp(494,36): warning C4267: '=': conversion from 'size_t' to 'irr::u32', possible loss of data
  (compiling source file '/src/irrmmd/SnPhyFluid.cpp')
  
  AppMainLib.vcxproj -> D:\AProj\VkUpApp\x64\Release\AppMainLib.lib
